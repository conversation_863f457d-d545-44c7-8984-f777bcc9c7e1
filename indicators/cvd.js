(function () {
  // Check if utils.js loaded correctly
  if (!window.utils) {
    console.debug("utils.js not loaded yet, retrying in 500ms...");
    setTimeout(() => {
      if (window.utils) {
        console.debug("utils.js loaded, proceeding with CVD initialization");
        // Re-initialize or proceed with logic here
      } else {
        console.debug("utils.js still not loaded, skipping CVD initialization");
      }
    }, 500);
    return; // Exit early to avoid errors
  }
  const { getIndicatorColor } = window.utils;
  const formatLargeNumber = window.commonUtils.formatLargeNumber;
  const { ema, stdev, clamp, lerp, weightedAverage } = window.mathUtils;

  // Configuration - use centralized config with fallback
  const CVD_CONFIG = window.PS?.CVD_CONFIG ||
    window.CONFIG?.cvd || {
      volumeMAPeriod: 90,
      volumeAdjustment: {
        enabled: true,
        buyMultiplier: 1.0,
        sellMultiplier: 1.0,
        useWicks: true,
        useBodySize: true,
        useCloseRelative: true,
      },
      renderOnCandleCloseOnly: true,
      lookbackPeriod: (() => {
        const savedWindow = localStorage.getItem("normalizationWindow");
        return savedWindow ? parseInt(savedWindow) : 1440;
      })(),
      normalize: true,
      smoothing: true,
      sensitivityMultiplier: 1.2,
      normalizationBuffer: 0,
      minSmoothingPeriod: 5,
      maxSmoothingPeriod: 20,
      adaptiveSmoothingFactor: 0.5,
      volumeWeighting: {
        enabled: true,
        weightFactor: 0.5,
      },
    };

  window.PS = window.PS || {};
  window.PS.CVD_CONFIG = CVD_CONFIG;

  // State Management
  const pendingCVDUpdates = {
    lastBarTime: 0,
    lastCvdValue: 0,
    pendingValue: 0,
    pendingEmaValue: 0,
    hasUpdate: false,
    avgVolume: 0,
  };

  let historicalCVDData = [];
  // let cvdUpdateInterval = null;

  // Access the global CVD data store via window (accessed dynamically to avoid undefined errors)
  let unsubscribeCVD = null;
  /**
   * Loads 6000 5-minute bars from Bybit using paginated API requests.
   * @param {string} symbol - e.g. 'BTC'
   * @returns {Promise<Array>} - Array of OHLCV bars
   */
  // Note: Spot CVD uses Bitstamp data (effectiveData.priceData) passed in via initializeCVDData
  // Historical data loading is handled by fetchBitstampHistoricalData in charts.js

  function createCVDChart(container, priceChart) {
    let cvdPane;
    try {
      const panes = priceChart.panes();
      if (panes && panes.length > 1) {
        cvdPane = panes[1];
        cvdPane.applyOptions({ visible: true });
        if (typeof cvdPane.setHeight === "function") {
          cvdPane.setHeight(150);
        }
        cvdPane.applyOptions({
          rightPriceScale: {
            visible: true,
            borderColor: "#2A2A2A",
            scaleMargins: { top: 0.1, bottom: 0.1 },
            formatter: {
              format: (price) => formatLargeNumber(price),
            },
          },
        });
      } else if (window.DEBUG_MODE) {
        console.debug("No CVD pane available");
      }
    } catch (e) {
      if (window.DEBUG_MODE) console.warn("Pane access error:", e);
    }

    const cvdSeries = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: {
          type: "volume",
          formatter: (price) => formatLargeNumber(price),
        },
        lineWidth: 1.5,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "SPOT CVD",
        pointsVisible: false,
        lastPriceAnimation: 0,
        autoscaleInfoProvider: () => ({
          priceRange: { minValue: -1.05, maxValue: 1.05 },
          margins: { above: 5, below: 5 },
        }),
        crosshairMarkerVisible: false,
      },
      1,
    );

    const cvdMASeries = {
      update: () => {},
      setData: () => {},
      applyOptions: () => {},
      _internal_isDisposed: false,
    };

    const zeroLine = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2, // match dashed style of other reference lines
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    // Add dashed reference lines at y=1 and y=-1
    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.cvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const levelMinus1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.cvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const referenceLines = {};
    referenceLines.level1 = level1Line;
    referenceLines.levelMinus1 = levelMinus1Line;

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    try {
      const chartContainer = container.querySelector(".tv-lightweight-charts");
      if (chartContainer) {
        chartContainer.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
      }
    } catch (e) {
      console.warn("Error styling chart container:", e);
    }

    return {
      chart: priceChart,
      pane: cvdPane,
      series: cvdSeries,
      zeroLine: zeroLine,
      referenceLines: referenceLines,
    };
  }

  // Shared color logic: red above 0.5, gray between, aqua below -0.5
  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)"; // red
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)"; // aqua
    } else {
      return "rgba(170, 170, 170, 0.8)"; // gray
    }
  }

  // Spot CVD: Calculate adjusted volume for a bar (Bitstamp/spot logic)
  function calculateAdjustedVolume(bar, prevBar) {
    if (!bar) return 0;
    const volume =
      bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
    if (volume === 0) return 0;
    // For spot, use close - open as delta direction
    let isBuyBar = true;
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useCloseRelative &&
      prevBar &&
      prevBar.close !== undefined &&
      !isNaN(prevBar.close)
    ) {
      isBuyBar = bar.close >= prevBar.close;
    } else {
      isBuyBar = bar.close >= bar.open;
    }
    let adjustmentFactor = 1.0;
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useBodySize
    ) {
      const bodySize = Math.abs(bar.close - bar.open);
      const range = bar.high - bar.low;
      if (range > 0 && isFinite(bodySize) && isFinite(range)) {
        const bodySizePercent = bodySize / range;
        adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
      }
    }
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useWicks
    ) {
      const totalRange = bar.high - bar.low;
      if (totalRange > 0 && isFinite(totalRange)) {
        const upperWick = bar.high - Math.max(bar.open, bar.close);
        const lowerWick = Math.min(bar.open, bar.close) - bar.low;
        if (isFinite(upperWick) && isFinite(lowerWick)) {
          if (isBuyBar) {
            const lowerWickPercent = lowerWick / totalRange;
            adjustmentFactor *= 1 + lowerWickPercent * 0.8;
          } else {
            const upperWickPercent = upperWick / totalRange;
            adjustmentFactor *= 1 + upperWickPercent * 0.8;
          }
        }
      }
    }
    adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
    // For spot, use buyMultiplier/sellMultiplier if present, else default to 1
    const buyMultiplier =
      (window.PS &&
        window.PS.CVD_CONFIG &&
        window.PS.CVD_CONFIG.volumeAdjustment &&
        window.PS.CVD_CONFIG.volumeAdjustment.buyMultiplier) ||
      1;
    const sellMultiplier =
      (window.PS &&
        window.PS.CVD_CONFIG &&
        window.PS.CVD_CONFIG.volumeAdjustment &&
        window.PS.CVD_CONFIG.volumeAdjustment.sellMultiplier) ||
      1;
    return isBuyBar
      ? volume * adjustmentFactor * buyMultiplier
      : -volume * adjustmentFactor * sellMultiplier;
  }

  // Spot CVD: Calculate CVD data array from price bars
  const cvdDataCache = new WeakMap();
  function calculateCVDData(priceData) {
    if (cvdDataCache.has(priceData)) return cvdDataCache.get(priceData);

    if (!priceData || priceData.length === 0) {
      console.warn("CVD: No price data provided for calculation");
      return [];
    }

    const cvdData = [];
    let cumulativeDelta = 0;
    let skippedBars = 0;

    for (let i = 0; i < priceData.length; i++) {
      const bar = priceData[i];
      const prevBar = i > 0 ? priceData[i - 1] : null;

      // Enhanced validation
      if (!bar || !bar.time || bar.volume === undefined || isNaN(bar.volume)) {
        skippedBars++;
        continue;
      }

      const barDelta = calculateAdjustedVolume(bar, prevBar);

      // Validate calculated delta
      if (isNaN(barDelta)) {
        console.warn(
          `CVD: Invalid delta calculated for bar at time ${bar.time}`,
        );
        skippedBars++;
        continue;
      }

      cumulativeDelta += barDelta;
      cvdData.push({ time: bar.time, value: cumulativeDelta });
    }

    if (skippedBars > 0) {
      console.warn(
        `CVD: Skipped ${skippedBars} invalid bars during calculation`,
      );
    }

    cvdDataCache.set(priceData, cvdData);
    return cvdData;
  }

  // In initializeCVDData or equivalent, standardize to only use closed bars
  function initializeCVDData(cvdComponents, priceData) {
    if (!priceData || priceData.length === 0) {
      console.error("CVD: Cannot initialize with empty price data");
      return { cvdData: [], cvdMAData: [] };
    }

    // Use only closed bars (exclude the last bar)
    const closedBars = priceData.slice(0, -1);
    const cvdData = calculateCVDData(closedBars);

    if (cvdData.length === 0) {
      console.error("CVD: No valid CVD data calculated");
      return { cvdData: [], cvdMAData: [] };
    }

    // Validate CVD data continuity
    const expectedDataPoints = priceData.filter(
      (bar) =>
        bar && bar.time && bar.volume !== undefined && !isNaN(bar.volume),
    ).length;

    if (cvdData.length < expectedDataPoints * 0.9) {
      console.warn(
        `CVD: Significant data loss detected. Expected ~${expectedDataPoints}, got ${cvdData.length}`,
      );
    }

    // Use centralized rollingNormalize from mathUtils
    const values = cvdData.map((d) => d.value);
    const lookbackPeriod =
      (window.PS &&
        window.PS.CVD_CONFIG &&
        window.PS.CVD_CONFIG.lookbackPeriod) ||
      864;
    const normArr = window.mathUtils.rollingNormalize(values, lookbackPeriod);
    const normalizedCVDData = cvdData.map((point, i) => {
      const normalizedValue = normArr[i];
      const color = getNormalizedColor(normalizedValue);
      return { time: point.time, value: normalizedValue, color: color };
    });

    const emptyMAData = cvdData.map((point) => ({
      time: point.time,
      value: 0,
    }));
    const zeroLineData = [];
    if (priceData.length > 0) {
      const firstTime = priceData[0].time;
      const lastTime = priceData[priceData.length - 1].time;
      zeroLineData.push({ time: firstTime, value: 0 });
      zeroLineData.push({ time: lastTime, value: 0 });
      const lastDataPoint = normalizedCVDData[normalizedCVDData.length - 1];
      if (lastDataPoint) {
        if (!window.PS) window.PS = {};
        if (!window.PS.pendingCVDUpdates) {
          window.PS.pendingCVDUpdates = {};
        }
        const pendingCVDUpdates = window.PS.pendingCVDUpdates;
        pendingCVDUpdates.lastBarTime = lastDataPoint.time;
        pendingCVDUpdates.pendingValue = lastDataPoint.value;
        pendingCVDUpdates.pendingEmaValue = 0;
        pendingCVDUpdates.lastCvdValue = cvdData[cvdData.length - 1].value;
        pendingCVDUpdates.hasUpdate = true;
      }
    } else {
      const now = Math.floor(Date.now() / 1000);
      zeroLineData.push({ time: now - 86400, value: 0 });
      zeroLineData.push({ time: now, value: 0 });
    }
    // Batch DOM updates for performance
    requestAnimationFrame(() => {
      cvdComponents.series.setData(normalizedCVDData);
      // Set price label color to match last value
      if (normalizedCVDData.length > 0) {
        const lastColor = normalizedCVDData[normalizedCVDData.length - 1].color;
        cvdComponents.series.applyOptions({
          priceLineColor: lastColor,
          lastValueVisible: false,
          priceLineVisible: false,
        });
      }
      cvdComponents.zeroLine.setData(zeroLineData);
      if (cvdComponents.referenceLines.level1)
        cvdComponents.referenceLines.level1.setData(
          zeroLineData.map((d) => ({ ...d, value: 1 })),
        );
      if (cvdComponents.referenceLines.levelMinus1)
        cvdComponents.referenceLines.levelMinus1.setData(
          zeroLineData.map((d) => ({ ...d, value: -1 })),
        );
    });
    return { cvdData: normalizedCVDData, cvdMAData: emptyMAData };
  }

  // Use window.IndicatorChartUtils for browser compatibility

  function synchronizeCharts(cvdComponents, priceChart) {
    // Use shared utility for zero/reference lines
    const syncHandle = window.IndicatorChartUtils
      ? window.IndicatorChartUtils.synchronizeCharts(
          cvdComponents,
          priceChart,
          {
            referenceLevels: {
              // Add reference levels if needed for coloredZeroLine, etc.
              // Example: level1: 1, levelMinus1: -1
            },
          },
        )
      : null;

    // Custom color update logic (specific to CVD)
    const updateIndicatorColor = () => {
      // Color update logic can be implemented here if needed
    };

    const colorUpdateInterval = setInterval(updateIndicatorColor, 1000);

    return {
      unsubscribe: () => {
        try {
          syncHandle.unsubscribe();
          clearInterval(colorUpdateInterval);
        } catch (e) {}
      },
      updateIndicatorColor: updateIndicatorColor,
    };
  }

  function normalizeCVDWithComponents(value, cvdComponents) {
    try {
      const lookbackData =
        cvdComponents.series.data && cvdComponents.series.data().length > 0
          ? cvdComponents.series
              .data()
              .slice(-window.PS.CVD_CONFIG.lookbackPeriod)
          : [];
      if (lookbackData.length === 0) {
        return value >= 0 ? 0.5 : -0.5; // Default if no data
      }
      const values = lookbackData.map((d) => d.value);
      const min = Math.min(...values);
      const max = Math.max(...values);
      if (min === max) return 0; // Avoid division by zero
      return ((value - min) / (max - min)) * 2 - 1;
    } catch (e) {
      console.debug("Error normalizing CVD with components:", e);
      return value >= 0 ? 0.5 : -0.5;
    }
  }

  function updateCVD(cvdComponents, bar, prevBar, lastCvdValue = 0) {
    if (!bar || bar.volume === undefined || isNaN(bar.volume)) {
      console.debug("Skipping CVD update due to invalid bar data");
      return lastCvdValue;
    }
    let timeGapMinutes = 0;
    if (prevBar && bar.time > prevBar.time) {
      timeGapMinutes = (bar.time - prevBar.time) / 60;
    }
    const volume =
      bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
    // You may need to adapt this calculation if you use a different function
    const barDelta = calculateAdjustedVolume(bar, prevBar);
    let weightedDelta = barDelta;
    if (
      window.PS.CVD_CONFIG.volumeWeighting &&
      window.PS.CVD_CONFIG.volumeWeighting.enabled &&
      volume > 0
    ) {
      let avgVolume = volume;
      if (pendingCVDUpdates && pendingCVDUpdates.avgVolume) {
        avgVolume = pendingCVDUpdates.avgVolume * 0.9 + volume * 0.1;
      }
      if (pendingCVDUpdates) {
        pendingCVDUpdates.avgVolume = avgVolume;
      }
      const volumeRatio = volume / avgVolume;
      const weightFactor = window.PS.CVD_CONFIG.volumeWeighting.weightFactor;
      weightedDelta =
        (barDelta * (1 + weightFactor * (volumeRatio - 1))) /
        (1 + weightFactor);
    }
    const newCvdValue = lastCvdValue + weightedDelta;
    let smoothedCvdValue = newCvdValue;
    if (timeGapMinutes > 5) {
      const prevCvdValue = pendingCVDUpdates.lastCvdValue || lastCvdValue;
      const basePeriod = 5;
      const additionalSmoothing =
        timeGapMinutes * window.PS.CVD_CONFIG.adaptiveSmoothingFactor;
      const adaptivePeriod = Math.min(
        window.PS.CVD_CONFIG.maxSmoothingPeriod,
        Math.max(
          window.PS.CVD_CONFIG.minSmoothingPeriod,
          basePeriod + additionalSmoothing,
        ),
      );
      const alpha = 2 / (adaptivePeriod + 1);
      smoothedCvdValue = alpha * newCvdValue + (1 - alpha) * prevCvdValue;
    }
    const normalizedCVDValue = normalizeCVDWithComponents(
      smoothedCvdValue,
      cvdComponents,
    );
    const displayValue = normalizedCVDValue;
    let cvdColor;
    if (displayValue >= 0) {
      cvdColor = "rgba(255, 0, 0, 0.8)"; // red for positive or zero
    } else {
      cvdColor = "rgba(0, 255, 255, 0.8)"; // aqua for negative
    }
    const newMaValue = 0;
    if (window.PS.CVD_CONFIG.renderOnCandleCloseOnly) {
      // When renderOnCandleCloseOnly is true, NEVER update the chart directly
      // Only update pending values and let renderPendingCVDUpdates handle chart updates
      if (bar.time !== pendingCVDUpdates.lastBarTime) {
        // New bar started - update the bar time but don't render
        pendingCVDUpdates.lastBarTime = bar.time;
        pendingCVDUpdates.pendingValue = displayValue;
        pendingCVDUpdates.pendingEmaValue = newMaValue;
        pendingCVDUpdates.lastCvdValue = newCvdValue;
        pendingCVDUpdates.hasUpdate = true;
      } else {
        // Same bar - just update pending values
        pendingCVDUpdates.pendingValue = displayValue;
        pendingCVDUpdates.pendingEmaValue = newMaValue;
        pendingCVDUpdates.lastCvdValue = newCvdValue;
        pendingCVDUpdates.hasUpdate = true;
      }
    } else {
      // When renderOnCandleCloseOnly is false, update chart immediately
      cvdComponents.series.update({
        time: bar.time,
        value: displayValue,
        color: cvdColor,
      });
      cvdComponents.series.applyOptions({
        priceLineColor: cvdColor,
        lastValueVisible: false,
        priceLineVisible: false,
      });
    }
    pendingCVDUpdates.lastCvdValue = newCvdValue;
    if (
      cvdComponents.syncResources &&
      typeof cvdComponents.syncResources.updateIndicatorColor === "function"
    ) {
      cvdComponents.syncResources.updateIndicatorColor();
    }
    return newCvdValue;
  }

  function resizeCVDChart(cvdComponents, _width, height) {
    try {
      if (cvdComponents && cvdComponents.chart) {
        if (cvdComponents.pane) {
          const cvdHeight = Math.max(150, Math.floor(height * 0.2));
          if (typeof cvdComponents.pane.setHeight === "function") {
            cvdComponents.pane.setHeight(cvdHeight);
          }
        } else {
          const chartContainer = document.querySelector(
            ".price-chart-container",
          );
          if (chartContainer) {
            const chartElement = chartContainer.querySelector(
              ".tv-lightweight-charts",
            );
            if (chartElement) {
              chartElement.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
              try {
                const panes = cvdComponents.chart.panes();
                if (panes && panes.length > 1) {
                  const cvdPane = panes[1];
                  cvdComponents.pane = cvdPane;
                  const cvdHeight = Math.max(150, Math.floor(height * 0.2));
                  if (typeof cvdPane.setHeight === "function") {
                    cvdPane.setHeight(cvdHeight);
                  }
                }
              } catch (paneError) {
                console.debug(
                  "Could not access panes API, falling back to DOM manipulation",
                );
                const paneElements = chartElement.querySelectorAll(
                  ".tv-lightweight-charts__pane",
                );
                if (paneElements && paneElements.length > 1) {
                  const cvdPaneElement = paneElements[1];
                  if (cvdPaneElement) {
                    cvdPaneElement.style.zIndex = "3";
                    cvdPaneElement.style.borderTop = "1px solid #2A2A2A";
                    cvdPaneElement.style.boxSizing = "border-box";
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {
      console.warn("Error resizing CVD chart:", e);
    }
  }

  function cleanupCVD(cvdComponents, syncResources) {
    // Use shared utility for cleanup if available
    if (
      window.IndicatorChartUtils &&
      window.IndicatorChartUtils.cleanupIndicator
    ) {
      window.IndicatorChartUtils.cleanupIndicator(
        cvdComponents,
        [
          /* cvdUpdateInterval removed */ typeof syncResources === "number"
            ? syncResources
            : undefined,
        ],
        {
          // pendingCVDUpdates state is removed
        },
      );
    } else {
      // Fallback cleanup
      if (unsubscribeCVD) {
        unsubscribeCVD();
        unsubscribeCVD = null;
      }
      // if (cvdUpdateInterval) { // cvdUpdateInterval removed
      //     clearInterval(cvdUpdateInterval);
      // }
    }
    // cvdUpdateInterval = null; // cvdUpdateInterval removed
  }

  // Performance-optimized rendering
  let lastRenderTime = 0; // Fixed: Uncommented the variable declaration
  function renderPendingCVDUpdates(cvdComponents) {
    // Fixed: Changed condition to check if series is NOT disposed
    if (!cvdComponents?.series || cvdComponents.series._internal_isDisposed) return;

    // Only render pending updates when renderOnCandleCloseOnly is enabled
    // This function is called on candle close to render the final value
    if (!window.PS.CVD_CONFIG.renderOnCandleCloseOnly) {
      return; // If real-time rendering is enabled, don't use this function
    }

    // Check if we have pending updates to render
    if (!pendingCVDUpdates.hasUpdate) {
      return; // No pending updates to render
    }

    // Throttle to prevent excessive rendering
    const now = Date.now();
    if (now - lastRenderTime < 1000) return; // Reduced throttle for candle close
    lastRenderTime = now;

    // Render the pending CVD value immediately (this is called on candle close)
    try {
      const value = pendingCVDUpdates.pendingValue;
      const cvdColor = getNormalizedColor(value);

      cvdComponents.series.update({
        time: pendingCVDUpdates.lastBarTime,
        value,
        color: cvdColor,
      });
      cvdComponents.series.applyOptions({
        priceLineColor: cvdColor,
        lastValueVisible: false,
        priceLineVisible: false,
      });

      // Mark as rendered
      pendingCVDUpdates.hasUpdate = false;

      if (window.DEBUG_MODE) {
        console.debug("CVD: Rendered pending update on candle close", {
          time: pendingCVDUpdates.lastBarTime,
          value: value
        });
      }
    } catch (e) {
      if (window.DEBUG_MODE) console.debug("CVD render error:", e);
    }
  }

  // Subscribe to global CVD data updates and update chart only if mounted
  function setupCVDUpdateInterval(cvdComponents) {
    if (unsubscribeCVD) {
      unsubscribeCVD();
      unsubscribeCVD = null;
    }
    if (!window.PS.subscribeCVD) {
      console.warn(
        "PS.subscribeCVD not available, skipping CVD update interval",
      );
      return null;
    }
    if (!window.PS.pendingCVDUpdates) {
      window.PS.pendingCVDUpdates = {
        lastBarTime: 0,
        pendingValue: 0,
        hasUpdate: false,
        lastCvdValue: 0,
      };
    }
    const pendingCVDUpdates = window.PS.pendingCVDUpdates;
    unsubscribeCVD = window.PS.subscribeCVD((cvdData) => {
      if (!cvdComponents || cvdComponents.series._internal_isDisposed) return;
      // Only update after bar close
      if (cvdData && cvdData.time && cvdData.value !== undefined) {
        if (cvdData.time !== pendingCVDUpdates.lastBarTime) {
          // Bar closed, push pending value
          if (pendingCVDUpdates.hasUpdate) {
            const color = getNormalizedColor(pendingCVDUpdates.pendingValue);
            cvdComponents.series.update({
              time: pendingCVDUpdates.lastBarTime,
              value: pendingCVDUpdates.pendingValue,
              color,
            });
            cvdComponents.series.applyOptions({
              priceLineColor: color,
              lastValueVisible: false,
              priceLineVisible: false,
            });
            pendingCVDUpdates.hasUpdate = false;
          }
          pendingCVDUpdates.lastBarTime = cvdData.time;
          pendingCVDUpdates.pendingValue = cvdData.value;
          pendingCVDUpdates.hasUpdate = true;
        } else {
          // Bar not closed, just update pending value
          pendingCVDUpdates.pendingValue = cvdData.value;
          pendingCVDUpdates.hasUpdate = true;
        }
      }
    });
    return unsubscribeCVD;
  }

  window.cvdModule = {
    createCVDChart,
    // calculateAdjustedVolume, // Removed
    // calculateCVDData, // Removed
    initializeCVDData,
    synchronizeCharts,
    updateCVD, // Role changed, may be further simplified or removed
    resizeCVDChart,
    cleanupCVD,
    renderPendingCVDUpdates, // Role changed
    setupCVDUpdateInterval,
    config: CVD_CONFIG,
    // Ensure unsubscribe logic is available for cleanup
    unsubscribeCVD: () => {
      if (unsubscribeCVD) {
        unsubscribeCVD();
        unsubscribeCVD = null;
      }
    },
  };
})();
