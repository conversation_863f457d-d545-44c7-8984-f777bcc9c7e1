// socket-profile5.0/indicators/perpimbalance.js
// Perp Imbalance visualization with fixed subscription system and NaN prevention

(function () {
  "use strict";

  // --- Ensure perpImbalance is always defined as an object at the very top ---
  if (!window.perpImbalance) window.perpImbalance = {};
  window.perpImbalance.createPerpImbalanceIndicator = () => ({ series: null });
  window.perpImbalance.initializeImbalanceData = () => ({});
  window.perpImbalance.synchronizeCharts = () => ({});

  // Configuration
  const PERP_IMBALANCE_CONFIG = window.CONFIG?.perpImbalance || {
    updateIntervalMs: 1000,
    lookbackPeriod: 1440,
    renderOnCandleCloseOnly: true, // Added to match CVD behavior
    colors: {
      positive: "#4CAF50",
      negative: "#F44336",
      neutral: "#9E9E9E",
    },
  };

  // Global state
  let unsubscribePerpImbalance = null;
  const pendingImbalanceUpdates = {
    lastBarTime: 0,
    pendingValue: 0,
    hasUpdate: false,
    lastImbalanceValue: 0,
    spotCVD: 0,
    futuresCVD: 0,
    connectionErrors: 0,
    lastSuccessTime: Date.now(),
    maxHistoryLength: 1000,
    normalizedData: [],
    spotBars: [],
    futuresBars: [],
    oiData: [],
  };

  // Persistent array to hold all perp imbalance values for proper normalization and chart updates
  const MAX_HISTORY = 6000; // Match full historical load
  let perpImbalanceHistory = [];

  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)"; // red for strong positive
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)"; // cyan for strong negative
    } else {
      return "rgba(170, 170, 170, 0.8)"; // gray for neutral
    }
  }

  // Rolling normalization utility
  function rollingNormalize(data, lookbackPeriod = 20) {
    if (!Array.isArray(data) || data.length === 0) return [];
    return data.map((value, index) => {
      const start = Math.max(0, index - lookbackPeriod + 1);
      const window = data.slice(start, index + 1);
      const min = Math.min(...window);
      const max = Math.max(...window);
      if (max === min) return 0;
      const normalized = 2 * ((value - min) / (max - min)) - 1;
      return isFinite(normalized) ? normalized : 0;
    });
  }

  // Handle undefined/null
  function normalizeImbalance(value, min, max) {
    if (
      !isFinite(value) ||
      !isFinite(min) ||
      !isFinite(max) ||
      min === undefined ||
      max === undefined ||
      min === max
    ) {
      return 0;
    }
    const normalized = 2 * ((value - min) / (max - min)) - 1;
    return isFinite(normalized) ? normalized : 0;
  }

  // Ensure global accessibility
  if (!window.normalizeImbalance) {
    window.normalizeImbalance = normalizeImbalance;
  }

  // Historical data fetch (6000 bars)
  async function load6000Bybit5mBars() {
    const TOTAL_BARS = 6000;
    const BATCH_SIZE = 1000;
    const INTERVAL = 5;
    const SYMBOL = "BTCUSDT";
    let allBars = [];
    let attempts = 0;
    const maxAttempts = 10;

    // Only log errors for production
    while (allBars.length < TOTAL_BARS && attempts < maxAttempts) {
      attempts++;
      try {
        const currentTime = Math.floor(Date.now() / 1000);
        const startTime =
          currentTime - (TOTAL_BARS - allBars.length) * INTERVAL * 60;

        const url = `https://api.bybit.com/v5/market/kline?category=linear&symbol=${SYMBOL}&interval=${INTERVAL}&start=${startTime * 1000}&limit=${BATCH_SIZE}`;
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.retCode !== 0 || !data.result?.list) {
          throw new Error(`API Error: ${data.retMsg || "Unknown error"}`);
        }

        const bars = data.result.list
          .map((bar) => ({
            time: Math.floor(parseInt(bar[0]) / 1000),
            open: parseFloat(bar[1]),
            high: parseFloat(bar[2]),
            low: parseFloat(bar[3]),
            close: parseFloat(bar[4]),
            volume: parseFloat(bar[5]),
          }))
          .filter(
            (bar) =>
              isFinite(bar.time) &&
              isFinite(bar.open) &&
              isFinite(bar.high) &&
              isFinite(bar.low) &&
              isFinite(bar.close) &&
              isFinite(bar.volume),
          )
          .sort((a, b) => a.time - b.time);

        if (bars.length === 0) {
          // Only warn if no valid bars in batch
          console.warn(`[perpImbalance] No valid bars in batch ${attempts}`);
          continue;
        }

        allBars = [...allBars, ...bars];
        allBars = allBars
          .filter(
            (bar, index, arr) =>
              arr.findIndex((b) => b.time === bar.time) === index,
          )
          .sort((a, b) => a.time - b.time)
          .slice(-TOTAL_BARS);

        if (allBars.length >= TOTAL_BARS) break;
        await new Promise((resolve) => setTimeout(resolve, 200));
      } catch (error) {
        console.error(`[perpImbalance] Batch ${attempts} failed:`, error);
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
    return allBars.slice(-TOTAL_BARS);
  }

  // Open Interest data fetch
  async function fetchBybitOpenInterest5m() {
    try {
      // Only log errors for production
      const SYMBOL = "BTCUSDT";
      const INTERVAL = "5min";
      const LIMIT = 200;
      const startTime = Date.now() - 50 * 24 * 60 * 60 * 1000;

      const url = `https://api.bybit.com/v5/market/open-interest?category=linear&symbol=${SYMBOL}&intervalTime=${INTERVAL}&startTime=${startTime}&limit=${LIMIT}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.retCode !== 0 || !data.result?.list) {
        throw new Error(`API Error: ${data.retMsg || "Unknown error"}`);
      }

      const oiData = data.result.list
        .map((item) => ({
          time: Math.floor(parseInt(item.timestamp) / 1000),
          openInterest: parseFloat(item.openInterest),
        }))
        .filter(
          (item) =>
            isFinite(item.time) &&
            isFinite(item.openInterest) &&
            item.openInterest > 0,
        )
        .sort((a, b) => a.time - b.time);

      const alignedData = [];
      const startTimeAligned = Math.floor(Date.now() / 1000) - 6000 * 5 * 60;

      for (let i = 0; i < 6000; i++) {
        const barTime = startTimeAligned + i * 5 * 60;
        const oiRecord = oiData.find(
          (item) => Math.abs(item.time - barTime) <= 150,
        );
        alignedData.push({
          time: barTime,
          openInterest: oiRecord ? oiRecord.openInterest : null,
        });
      }
      return alignedData;
    } catch (error) {
      console.error(
        "[perpImbalance] Failed to fetch open interest data:",
        error,
      );
      return [];
    }
  }

  function createPerpImbalanceIndicator(priceChart) {
    if (!priceChart) {
      console.error("PerpImbalance: priceChart is undefined. Cannot create indicator.");
      return null;
    }
    let indicatorPane;
    let paneIndex = 1;
    try {
      const panes = priceChart.panes && priceChart.panes();
      if (panes && panes.length > 1) {
        indicatorPane = panes[1];
        paneIndex = 1;
      } else if (panes && panes.length > 0) {
        indicatorPane = panes[0];
        paneIndex = 0;
      } else {
        indicatorPane = priceChart;
        paneIndex = 0;
      }
      window.perpImbalancePaneIndex = paneIndex;
    } catch (e) {
      console.error("Error accessing panes:", e);
      indicatorPane = priceChart;
      paneIndex = 0;
      window.perpImbalancePaneIndex = 0;
    }

    let indicatorSeries;
    try {
      if (!priceChart.addSeries) {
        throw new Error("priceChart.addSeries is undefined");
      }
      indicatorSeries = priceChart.addSeries(
        LightweightCharts.LineSeries,
        {
          priceFormat: { type: "volume", precision: 2, minMove: 0.01 },
          lineWidth: 2,
          color: "rgba(170, 170, 170, 0.8)",
          lastValueVisible: false,
          priceLineVisible: false,
          title: "PERP IMB.",
          pointsVisible: false,
          lastPriceAnimation: 0,
          crosshairMarkerVisible: false,
          autoscaleInfoProvider: () => ({
            priceRange: { minValue: -1.05, maxValue: 1.05 },
            margins: { above: 5, below: 5 },
          }),
        },
        paneIndex,
      );
      indicatorSeries.applyOptions({
        color: "rgba(170, 170, 170, 0.8)",
      });
    } catch (error) {
      console.error("Error creating perpImbalance series:", error);
      return null;
    }

    let zeroLine;
    try {
      zeroLine = priceChart.addSeries(
        LightweightCharts.LineSeries,
        {
          priceFormat: { type: "volume" },
          color: "#444444",
          lineWidth: 1,
          lineStyle: 2,
          lastValueVisible: false,
          priceLineVisible: false,
          title: "",
          pointMarkersVisible: false,
          lastPriceAnimation: 0,
          crosshairMarkerVisible: false,
        },
        paneIndex,
      );
    } catch (error) {
      console.error("Error creating zero line:", error);
      zeroLine = priceChart.addSeries(LightweightCharts.LineSeries, {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointMarkersVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      });
    }

    // Fix variable redeclaration for levelMinus1Line
    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "rgba(170, 170, 170, 0.8)",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointMarkersVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      paneIndex,
    );

    const levelMinus1Line_ = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "rgba(170, 170, 170, 0.8)",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointMarkersVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      paneIndex,
    );

    const referenceLines = { level1: level1Line, levelMinus1: levelMinus1Line_ };

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    const components = {
      chart: priceChart,
      pane: indicatorPane,
      series: indicatorSeries,
      zeroLine,
      referenceLines,
    };
    disableCrosshairMarkers(components);
    startImbalanceRenderLoop(components);
    return components;
  }

  function initializeImbalanceData(components, bybit5m, bitstamp5m) {
    if (!bybit5m?.length || !bitstamp5m?.length) {
      console.error("PerpImbalance: Cannot initialize with empty data");
      return { imbalanceData: [] };
    }

    try {
      const minLen = Math.min(bybit5m.length, bitstamp5m.length);
      let bybitAligned = bybit5m.slice(-minLen);
      let bitstampAligned = bitstamp5m.slice(-minLen);

      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const lastBarTime = bybitAligned[bybitAligned.length - 1]?.time;
      const isLastBarClosed =
        lastBarTime && lastBarTime <= now - (now % barInterval);
      if (!isLastBarClosed) {
        bybitAligned = bybitAligned.slice(0, -1);
        bitstampAligned = bitstampAligned.slice(0, -1);
      }

      if (bybitAligned.length === 0 || bitstampAligned.length === 0) {
        console.warn("PerpImbalance: No closed bars available for calculation");
        return { imbalanceData: [] };
      }

      function priceFlow(arr) {
        return arr.map((bar) => {
          if (
            !bar ||
            typeof bar.close !== "number" ||
            typeof bar.open !== "number" ||
            typeof bar.volume !== "number"
          ) {
            return 0;
          }
          return (bar.close - bar.open) * bar.volume;
        });
      }
      function cumulativeDelta(arr) {
        let sum = 0;
        return arr.map((v) => {
          if (isNaN(v)) v = 0;
          return (sum += v);
        });
      }

      const pfBybit = priceFlow(bybitAligned);
      const pfBitstamp = priceFlow(bitstampAligned);
      const cdBybit = cumulativeDelta(pfBybit);
      const cdBitstamp = cumulativeDelta(pfBitstamp);
      const diff = cdBitstamp.map((v, i) => v - cdBybit[i]);
      // Use a consistent normalization window (lookbackPeriod)
      const liqs = rollingNormalize(diff, PERP_IMBALANCE_CONFIG.lookbackPeriod);

      const imbalanceData = bybitAligned.map((bar, i) => {
        let v = liqs[i];
        if (isNaN(v)) v = 0;
        return { time: bar.time, value: v, color: getNormalizedColor(v) };
      });

      // --- FIX: Initialize perpImbalanceHistory with historical raw values ---
      perpImbalanceHistory = bybitAligned.map((bar, i) => ({
        time: bar.time,
        rawValue: diff[i] || 0,
      }));
      if (perpImbalanceHistory.length > MAX_HISTORY) {
        perpImbalanceHistory = perpImbalanceHistory.slice(-MAX_HISTORY);
      }

      if (!window.PS) window.PS = {};
      if (!window.PS.pendingPerpImbalanceUpdates) {
        window.PS.pendingPerpImbalanceUpdates = {};
      }
      const globalPendingUpdates = window.PS.pendingPerpImbalanceUpdates;

      if (imbalanceData.length > 0) {
        const lastDataPoint = imbalanceData[imbalanceData.length - 1];
        globalPendingUpdates.lastBarTime = lastDataPoint.time;
        globalPendingUpdates.pendingValue = lastDataPoint.value;
        globalPendingUpdates.hasUpdate = true;
        // Initialize CVD state from historical data
        const lastSpotCVD = cdBitstamp[cdBitstamp.length - 1] || 0;
        const lastFuturesCVD = cdBybit[cdBybit.length - 1] || 0;
        globalPendingUpdates.spotCVD = lastSpotCVD;
        globalPendingUpdates.futuresCVD = lastFuturesCVD;
        globalPendingUpdates.isInitialized = true;
      }

      requestAnimationFrame(() => {
        if (components && components.series && !components.series._internal_isDisposed) {
          components.series.setData(imbalanceData);
          if (imbalanceData.length > 0) {
            const lastColor = imbalanceData[imbalanceData.length - 1].color;
            components.series.applyOptions({
              color: lastColor,
              priceLineColor: lastColor,
              lastValueVisible: false,
              priceLineVisible: false,
              title: 'PERP IMB.',
              titleColor: lastColor,
            });
          }
          if (components.zeroLine) {
            const zeroLineData = [];
            if (imbalanceData.length > 0) {
              zeroLineData.push({ time: imbalanceData[0].time, value: 0 });
              zeroLineData.push({ time: imbalanceData[imbalanceData.length - 1].time, value: 0 });
            }
            components.zeroLine.setData(zeroLineData);
          }
          if (components.referenceLines.level1)
            components.referenceLines.level1.setData(
              imbalanceData.map((d) => ({ time: d.time, value: 1 })),
            );
          if (components.referenceLines.levelMinus1)
            components.referenceLines.levelMinus1.setData(
              imbalanceData.map((d) => ({ time: d.time, value: -1 })),
            );
        }
      });

      return { imbalanceData };
    } catch (e) {
      console.error("PerpImbalance: Failed to initialize imbalance data:", e);
      return { imbalanceData: [] };
    }
  }

  function synchronizeCharts(components, priceChart) {
    return {};
  }

  function cleanupIndicator(components) {
    if (!components) return;

    try {
      if (components.series && !components.series._internal_isDisposed) {
        components.chart.removeSeries(components.series);
      }
    } catch (e) {
      console.debug("Error removing main series:", e);
    }

    try {
      if (components.zeroLine && !components.zeroLine._internal_isDisposed) {
        components.chart.removeSeries(components.zeroLine);
      }
    } catch (e) {
      console.debug("Error removing zero line:", e);
    }

    try {
      if (components.referenceLines) {
        Object.values(components.referenceLines).forEach((line) => {
          if (line && !line._internal_isDisposed) {
            components.chart.removeSeries(line);
          }
        });
      }
    } catch (e) {
      console.debug("Error removing reference lines:", e);
    }

    if (components.syncResources?.cleanup) {
      try {
        components.syncResources.cleanup();
      } catch (e) {
        console.debug("Error during sync resources cleanup:", e);
      }
    }

    if (unsubscribePerpImbalance) {
      try {
        unsubscribePerpImbalance();
        unsubscribePerpImbalance = null;
      } catch (e) {
        console.debug("Error during perpImbalance unsubscribe:", e);
      }
    }

    try {
      Object.keys(components).forEach((key) => {
        components[key] = null;
      });
    } catch (e) {
      console.debug("Error clearing component references:", e);
    }
  }

  function disableCrosshairMarkers(components) {
    try {
      if (components.series) {
        components.series.applyOptions({ crosshairMarkerVisible: false });
      }
      if (components.zeroLine) {
        components.zeroLine.applyOptions({ crosshairMarkerVisible: false });
      }
      if (components.referenceLines) {
        Object.values(components.referenceLines).forEach((line) => {
          if (line) {
            line.applyOptions({ crosshairMarkerVisible: false });
          }
        });
      }
    } catch (e) {
      console.debug("Error disabling crosshair markers:", e);
    }
  }

  // Refactor renderPendingImbalanceUpdates: This function's role will be minimized or removed.
  // Chart updates should primarily be driven by setupPerpUpdateInterval.
  // The use of setData here is inefficient for live updates.
  function renderPendingImbalanceUpdates(components) {
    if (!components?.series || components.series._internal_isDisposed) return;

    // Only render pending updates when renderOnCandleCloseOnly is enabled
    // This function is called on candle close to render the final value
    if (!PERP_IMBALANCE_CONFIG.renderOnCandleCloseOnly) {
      return; // If real-time rendering is enabled, don't use this function
    }

    // Check if we have pending updates to render
    if (!window.PS?.pendingPerpImbalanceUpdates?.hasUpdate) {
      return; // No pending updates to render
    }

    // Render the pending PERP Imbalance value immediately (this is called on candle close)
    try {
      const pendingUpdates = window.PS.pendingPerpImbalanceUpdates;
      const value = pendingUpdates.pendingValue;

      if (value === undefined || isNaN(value) || !isFinite(value)) {
        return;
      }

      const color = getNormalizedColor(value);

      components.series.update({
        time: pendingUpdates.lastBarTime,
        value,
        color,
      });
      components.series.applyOptions({
        priceLineColor: color,
        lastValueVisible: false,
        priceLineVisible: false,
        title: 'PERP IMB.',
        titleColor: color,
      });

      // Mark as rendered
      pendingUpdates.hasUpdate = false;

      if (window.DEBUG_MODE) {
        console.debug("PERP Imbalance: Rendered pending update on candle close", {
          time: pendingUpdates.lastBarTime,
          value: value
        });
      }
    } catch (e) {
      if (window.DEBUG_MODE) console.debug("PERP Imbalance render error:", e);
    }
  }

  // Alias for charts.js compatibility
  function renderPendingUpdates(components) {
    return renderPendingImbalanceUpdates(components);
  }

  // The startImbalanceRenderLoop calls renderPendingImbalanceUpdates.
  // If renderPendingImbalanceUpdates is removed or heavily altered, this loop might also be unnecessary.
  let lastImbalanceRenderTime = 0;
  function startImbalanceRenderLoop(components) {
    /*
    function renderLoop() {
      const now = Date.now();
      if (now - lastImbalanceRenderTime > 2000) { // 2 seconds throttle
        // renderPendingImbalanceUpdates(components); // Call is now conditional or removed
        lastImbalanceRenderTime = now;
      }
      requestAnimationFrame(renderLoop);
    }
    requestAnimationFrame(renderLoop);
    */
  }

  function setupPerpUpdateInterval(components) {
    if (unsubscribePerpImbalance) {
      try { unsubscribePerpImbalance(); } catch(e) {}
      unsubscribePerpImbalance = null;
    }
    if (!window.PS.subscribePerpImbalance) {
      console.warn("PERP IMBALANCE: window.PS.subscribePerpImbalance not available.");
      return null;
    }

    // Ensure pendingPerpImbalanceUpdates (the global one) is initialized
    if (!window.PS.pendingPerpImbalanceUpdates) {
      window.PS.pendingPerpImbalanceUpdates = {
        lastBarTime: 0, // Should be set by initializeImbalanceData
        pendingValue: 0, // Should be set by initializeImbalanceData
        hasUpdate: false,
        lastImbalanceValue: 0, // Raw imbalance if needed
        // Other state properties like spotCVD, futuresCVD, connectionErrors etc.
        connectionErrors: 0,
        lastSuccessTime: Date.now(),
      };
    }
    // Use the global pendingUpdates object, aliased for convenience
    const pendingUpdates = window.PS.pendingPerpImbalanceUpdates;

    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && pendingUpdates.hasUpdate) {
        // If tab becomes visible and there's a pending update, render it.
        try {
          if (isFinite(pendingUpdates.pendingValue) && pendingUpdates.lastBarTime > 0) {
            const color = getNormalizedColor(pendingUpdates.pendingValue);
            components.series.update({
              time: pendingUpdates.lastBarTime,
              value: pendingUpdates.pendingValue,
              color,
            });
            components.series.applyOptions({
              priceLineColor: color,
              lastValueVisible: false,
              priceLineVisible: false,
              title: 'PERP IMB.', // Keep title consistent
              titleColor: color   // Match line color for label
            });
            // Potentially set pendingUpdates.hasUpdate = false here if this render fulfills the update.
            // However, the main subscription should be the authority.
          }
        } catch (e) {
          console.warn("PERP IMBALANCE: Error updating chart on visibility change:", e);
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Listen for WebSocket visibility restoration events
    const handleVisibilityRestoration = (event) => {
      console.log('[PERP IMBALANCE] WebSocket visibility restored after', event.detail.hiddenDuration, 'ms');

      // Force reconnection of imbalance subscription after visibility restoration
      setTimeout(() => {
        if (pendingUpdates.connectionErrors > 0 ||
            Date.now() - (pendingUpdates.lastSuccessTime || 0) > 30000) {
          console.log('[PERP IMBALANCE] Forcing subscription reconnection after visibility restoration');
          attemptReconnection();
        }
      }, 2000);
    };

    window.addEventListener('websocket-visibility-restored-bybit', handleVisibilityRestoration);

    let reconnectAttempts = 0;
    const maxReconnectAttempts = 3;
    const reconnectDelay = 5000;

    const createSubscription = () => {
      try {
        unsubscribePerpImbalance = window.PS.subscribePerpImbalance(
          "BTCUSDT", // Symbol, ensure this is configurable or correct
          "5min",    // Interval, ensure this is configurable or correct
          (imbalanceData) => {
            if (!components || components.series._internal_isDisposed) {
              return;
            }
            if (!imbalanceData || typeof imbalanceData.time !== 'number' || typeof imbalanceData.value === 'undefined') {
              console.warn("PERP IMBALANCE: Received invalid data from subscription:", imbalanceData);
              return;
            }

            try {
              pendingUpdates.connectionErrors = 0;
              pendingUpdates.lastSuccessTime = Date.now();
              reconnectAttempts = 0;

              if (!isFinite(imbalanceData.value)) {
                console.warn("PERP IMBALANCE: Received non-finite imbalance value:", imbalanceData.value);
                return;
              }

              // The subscribed 'imbalanceData.value' should be the ALREADY NORMALIZED value for the bar.
              // If it's a raw value, normalization needs to happen here or in the data store before publishing.

              // Calculate current 5-minute bar time
              const now = Math.floor(Date.now() / 1000);
              const currentBarTime = Math.floor(now / 300) * 300; // 5-minute intervals

              // Check if we have a new 5-minute bar (bar close event)
              if (currentBarTime > pendingUpdates.lastBarTime) {
                // New bar detected - render the previous bar's final value only if renderOnCandleCloseOnly is false
                if (!PERP_IMBALANCE_CONFIG.renderOnCandleCloseOnly && pendingUpdates.hasUpdate && isFinite(pendingUpdates.pendingValue) && pendingUpdates.lastBarTime > 0) {
                  const color = getNormalizedColor(pendingUpdates.pendingValue);
                  components.series.update({
                    time: pendingUpdates.lastBarTime,
                    value: pendingUpdates.pendingValue,
                    color: color,
                  });
                  components.series.applyOptions({
                    priceLineColor: color,
                    lastValueVisible: false,
                    priceLineVisible: false,
                    title: 'PERP IMB.',
                    titleColor: color
                  });
                }
                // Update to new bar time and set pending value
                pendingUpdates.lastBarTime = currentBarTime;
                pendingUpdates.pendingValue = imbalanceData.value; // Store the normalized value
                pendingUpdates.hasUpdate = true;

              } else if (imbalanceData.time >= pendingUpdates.lastBarTime) {
                // Same bar - just update pending value (don't render yet when renderOnCandleCloseOnly is true)
                pendingUpdates.pendingValue = imbalanceData.value; // Store the normalized value
                pendingUpdates.hasUpdate = true;

                // If renderOnCandleCloseOnly is false, render immediately
                if (!PERP_IMBALANCE_CONFIG.renderOnCandleCloseOnly) {
                  const color = getNormalizedColor(imbalanceData.value);
                  components.series.update({
                    time: pendingUpdates.lastBarTime,
                    value: imbalanceData.value,
                    color: color,
                  });
                  components.series.applyOptions({
                    priceLineColor: color,
                    lastValueVisible: false,
                    priceLineVisible: false,
                    title: 'PERP IMB.',
                    titleColor: color
                  });
                }
              }
              // Older data (imbalanceData.time < pendingUpdates.lastBarTime) is ignored.

            } catch (e) {
              console.error("PERP IMBALANCE: Error processing subscribed data:", e);
              pendingUpdates.connectionErrors = (pendingUpdates.connectionErrors || 0) + 1;
              if (pendingUpdates.connectionErrors > 5) {
                attemptReconnection(); // Attempt to reconnect if processing fails repeatedly
              }
            }
          }
        );
      } catch (e) {
        console.error("PERP IMBALANCE: Failed to create subscription:", e);
        attemptReconnection(); // Attempt to reconnect if subscription setup fails
      }
    };

    let healthCheckInterval = null; // Declare here so it can be accessed in attemptReconnection

    const attemptReconnection = () => {
      if (reconnectAttempts >= maxReconnectAttempts) {
        console.warn("PERP IMBALANCE: Max reconnect attempts reached. Stopping health check.");
        // Clear the health check interval to stop further reconnection attempts
        if (healthCheckInterval) {
          clearInterval(healthCheckInterval);
        }
        return;
      }
      reconnectAttempts++;
      console.warn(`PERP IMBALANCE: Reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts}`);

      if (unsubscribePerpImbalance) {
        try { unsubscribePerpImbalance(); } catch (e) { /* ignore */ }
        unsubscribePerpImbalance = null;
      }

      setTimeout(() => {
        if (components && !components.series._internal_isDisposed) {
          createSubscription();
        }
      }, reconnectDelay * reconnectAttempts);
    };

    healthCheckInterval = setInterval(() => {
        if (Date.now() - (pendingUpdates.lastSuccessTime || 0) > 30000) { // 30 secs
            console.warn("PERP IMBALANCE: No data for 30s, attempting reconnect.");
            attemptReconnection();
        }

        // Check for bar close events every 10 seconds to ensure we don't miss any
        const now = Math.floor(Date.now() / 1000);
        const currentBarTime = Math.floor(now / 300) * 300; // 5-minute intervals

        if (currentBarTime > pendingUpdates.lastBarTime && pendingUpdates.hasUpdate) {
          // We have a new bar and pending data - render it only if renderOnCandleCloseOnly is false
          if (!PERP_IMBALANCE_CONFIG.renderOnCandleCloseOnly && isFinite(pendingUpdates.pendingValue) && pendingUpdates.lastBarTime > 0) {
            const color = getNormalizedColor(pendingUpdates.pendingValue);
            try {
              components.series.update({
                time: pendingUpdates.lastBarTime,
                value: pendingUpdates.pendingValue,
                color: color,
              });
              components.series.applyOptions({
                priceLineColor: color,
                lastValueVisible: false,
                priceLineVisible: false,
                title: 'PERP IMB.',
                titleColor: color
              });
              console.log(`PERP IMBALANCE: Bar close update at ${new Date(pendingUpdates.lastBarTime * 1000).toISOString()}, value: ${pendingUpdates.pendingValue.toFixed(4)}`);
            } catch (e) {
              console.warn("PERP IMBALANCE: Error in health check bar close update:", e);
            }
          }
          // Update to current bar time
          pendingUpdates.lastBarTime = currentBarTime;
          // Only reset hasUpdate if we're not using renderOnCandleCloseOnly (so renderPendingUpdates can handle it)
          if (!PERP_IMBALANCE_CONFIG.renderOnCandleCloseOnly) {
            pendingUpdates.hasUpdate = false;
          }
        }
    }, 10000); // Check every 10 secs

    createSubscription();

    // Return cleanup function
    return () => {
        if(unsubscribePerpImbalance) {
            try { unsubscribePerpImbalance(); } catch(e) {}
            unsubscribePerpImbalance = null;
        }
        if(healthCheckInterval) {
            clearInterval(healthCheckInterval);
        }
        document.removeEventListener("visibilitychange", handleVisibilityChange);
        window.removeEventListener('websocket-visibility-restored-bybit', handleVisibilityRestoration);
    };
  }

  // Initial load and subscription setup
  (async function () {
    try {
      // Wait for window.priceChart to be available
      const waitForPriceChart = new Promise((resolve) => {
        const checkExist = setInterval(() => {
          if (window.priceChart) {
            clearInterval(checkExist);
            resolve(window.priceChart);
          }
        }, 200);
      });
      const [bybit5m, bitstamp5m, priceChart] = await Promise.all([
        load6000Bybit5mBars(),
        fetchBybitOpenInterest5m(),
        waitForPriceChart,
      ]);

      // Precompute imbalance data outside animation frame
      let components = createPerpImbalanceIndicator(priceChart);
      if (!components) {
        throw new Error("Failed to create PerpImbalance indicator components.");
      }
      cleanupIndicator(components);
      components.chart.setVisible(true);

      const { imbalanceData } = initializeImbalanceData(components, bybit5m, bitstamp5m);

      // Initial render (only setData, no heavy work)
      requestAnimationFrame(() => {
        if (components.series && !components.series._internal_isDisposed) {
          components.series.setData(imbalanceData);
        }
      });

      // Setup perp update interval
      setupPerpUpdateInterval(components);

      // Add a backup mechanism to ensure updates happen every 5 minutes
      setInterval(() => {
        const now = Math.floor(Date.now() / 1000);
        const currentBarTime = Math.floor(now / 300) * 300;
        const secondsIntoBar = now % 300;

        // Trigger at the start of each 5-minute bar
        if (secondsIntoBar <= 5) {
          console.log(`PERP IMBALANCE: Backup 5-minute update trigger at ${new Date().toISOString()}`);
          // Force a data store update
          if (window.PS && window.PS.setPerpImbalanceSourceData && bybit5m && bitstamp5m) {
            window.PS.setPerpImbalanceSourceData({
              spot: bitstamp5m,
              futures: bybit5m,
              oi: []
            });
            // Also trigger a synthetic update
            if (window.PS.setPerpImbalance && window.PS.pendingPerpImbalanceUpdates) {
              const pendingUpdates = window.PS.pendingPerpImbalanceUpdates;
              if (pendingUpdates.pendingValue !== undefined) {
                window.PS.setPerpImbalance({
                  time: currentBarTime,
                  value: pendingUpdates.pendingValue
                });
              }
            }
          }
        }
      }, 30000); // Check every 30 seconds

    } catch (e) {
      console.error("PerpImbalance: Initialization error:", e);
    }
  })();

  // --- Module registration: ensure perpImbalance is available globally ---
  // Overwrite with real implementations after they are defined
  window.perpImbalance.createPerpImbalanceIndicator = createPerpImbalanceIndicator;
  window.perpImbalance.initializeImbalanceData = initializeImbalanceData;
  window.perpImbalance.synchronizeCharts = synchronizeCharts;
  window.perpImbalance.renderPendingUpdates = renderPendingUpdates;
  // Add any other public methods here as needed
})();
